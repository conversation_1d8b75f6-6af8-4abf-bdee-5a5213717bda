import { ArrowUpDown } from "lucide-react";
import { Player } from "@/types/ranking";

interface PlayersTableProps {
  players: Player[];
  onPlayerClick: (player: Player) => void;
  sortConfig: {
    key: "name" | "totalPoints";
    direction: "asc" | "desc";
  };
  onSort: (key: "name" | "totalPoints") => void;
}

export function PlayersTable({ 
  players, 
  onPlayerClick, 
  sortConfig, 
  onSort 
}: PlayersTableProps) {
  return (
    <table className="w-full">
      <thead>
        <tr className="bg-blue-900/30">
          <th className="px-4 py-2.5 text-left">
            <button
              className="flex items-center gap-2 text-sm font-medium text-blue-300"
              onClick={() => onSort("name")}
            >
              Giocatore
              <ArrowUpDown size={14} className={sortConfig.key === "name" ? "opacity-100" : "opacity-50"} />
            </button>
          </th>
          <th className="px-4 py-2.5 text-left">
            <button
              className="flex items-center gap-2 text-sm font-medium text-blue-300"
              onClick={() => onSort("totalPoints")}
            >
              Totale Punti
              <ArrowUpDown 
                size={14} 
                className={sortConfig.key === "totalPoints" ? "opacity-100" : "opacity-50"}
                style={{ 
                  transform: sortConfig.key === "totalPoints" && sortConfig.direction === "desc" 
                    ? "scaleY(-1)" 
                    : undefined 
                }}
              />
            </button>
          </th>
          <th className="px-4 py-2.5 text-left">
            <span className="text-sm font-medium text-blue-300">
              Tornei Giocati
            </span>
          </th>
        </tr>
      </thead>
      <tbody>
        {players.map((player, index) => {
const tournamentsPlayed = (player.tournamentsPlayed ?? (player.tournament_results?.length || 0));

          // Use original ranking position if available, otherwise fall back to current index
          const rankingPosition = player.originalRankingPosition ?? (index + 1);

          // Stile unificato per i primi 8 giocatori (basato sulla posizione originale)
          const isTopEight = rankingPosition <= 8;

          return (
            <tr
              key={player.id}
              className={`border-t border-blue-500/20 hover:bg-blue-900/20 cursor-pointer
                ${isTopEight ? "bg-blue-600/15 border border-blue-400 shadow-[0_0_12px_rgba(99,102,241,0.5)]" : ""}`}
              onClick={() => onPlayerClick(player)}
            >
              <td className="px-4 py-2.5">
                <div className="flex items-center gap-3">
                  <div
                    className={`w-7 h-7 rounded-full flex items-center justify-center text-sm font-medium ${
                      rankingPosition === 1
                        ? "bg-yellow-500/20 text-yellow-300"
                        : rankingPosition === 2
                        ? "bg-gray-400/20 text-gray-300"
                        : rankingPosition === 3
                        ? "bg-amber-700/20 text-amber-600"
                        : "bg-blue-500/20 text-blue-300"
                    }`}
                  >
                    {rankingPosition}
                  </div>
                  <span className="font-medium">{player.name}</span>
                </div>
              </td>
              <td className="px-4 py-2.5">
                <span className="font-medium">
                  {player.totalPoints} pt
                </span>
              </td>
              <td className="px-4 py-2.5">
                <span className="font-medium">
                  {tournamentsPlayed}
                </span>
              </td>
            </tr>
          );
        })}
      </tbody>
    </table>
  );
} 