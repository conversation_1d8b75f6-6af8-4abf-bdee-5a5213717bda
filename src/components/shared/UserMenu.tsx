'use client';

import { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { useAuthContext } from '@/lib/providers/AuthProvider';
import { LogOut, ChevronDown, Settings, User } from 'lucide-react';
import Link from 'next/link';
import { useIsAdmin } from '@/hooks/useIsAdmin';
import { getDisplayName } from '@/lib/utils';

export function UserMenu() {
  const { user, player, signOut, loading } = useAuthContext();
  const { isAdmin, loading: adminLoading } = useIsAdmin();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [mounted, setMounted] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, right: 0 });

  // Imposta mounted a true dopo il montaggio del componente
  useEffect(() => {
    setMounted(true);
  }, []);

  // Calcola la posizione del dropdown quando si apre
  const updateDropdownPosition = () => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const dropdownWidth = 192; // w-48 = 192px
      const isMobile = window.innerWidth < 640; // sm breakpoint

      let rightPosition;

      if (isMobile) {
        // Mobile: l'angolo sinistro del dropdown deve essere sotto l'angolo sinistro del bottone
        rightPosition = window.innerWidth - rect.left - dropdownWidth + window.scrollX;
      } else {
        // Desktop: l'angolo destro del dropdown deve coincidere con l'angolo destro del bottone
        rightPosition = window.innerWidth - rect.right + window.scrollX;
      }

      // Se il dropdown andrebbe fuori schermo a sinistra, aggiustalo
      if (rect.left < 8 && isMobile) {
        rightPosition = window.innerWidth - 8 - dropdownWidth + window.scrollX;
      } else if (rect.right - dropdownWidth < 8 && !isMobile) {
        rightPosition = window.innerWidth - rect.left - dropdownWidth + window.scrollX + 8;
      }

      setDropdownPosition({
        top: rect.bottom + window.scrollY + 4, // 4px gap like mt-1
        right: Math.max(8, rightPosition) // Minimo 8px dal bordo
      });
    }
  };

  // Chiudi il dropdown quando si clicca fuori
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Aggiorna la posizione quando il dropdown si apre o la finestra viene ridimensionata
  useEffect(() => {
    if (isOpen) {
      updateDropdownPosition();

      const handleResize = () => updateDropdownPosition();
      const handleScroll = () => updateDropdownPosition();

      window.addEventListener('resize', handleResize);
      window.addEventListener('scroll', handleScroll);

      return () => {
        window.removeEventListener('resize', handleResize);
        window.removeEventListener('scroll', handleScroll);
      };
    }
  }, [isOpen]);

  // Salva l'URL corrente per il reindirizzamento post-logout
  const handleLogout = async () => {
    if (typeof window !== 'undefined') {
      // Salva l'URL corrente prima del logout
      const currentPath = window.location.pathname + window.location.search;
      localStorage.setItem('logoutReturnUrl', currentPath);
    }
    await signOut();
    setIsOpen(false);
  };

  // Non renderizzare nulla durante il rendering lato server o prima del montaggio
  // Questo previene errori di idratazione
  if (!mounted) {
    return (
      <div className="w-[120px] h-[36px] bg-blue-500/10 border border-blue-500/30 rounded-md"></div>
    );
  }

  // Mostra un placeholder durante il caricamento
  if (loading || adminLoading) {
    return (
      <div className="w-[120px] h-[36px] bg-blue-500/10 border border-blue-500/30 rounded-md"></div>
    );
  }

  // Se l'utente non è autenticato, mostra il pulsante "Area Riservata"
  if (!user) {
    // Salva l'URL corrente per il reindirizzamento post-login
    const handleLoginClick = () => {
      if (typeof window !== 'undefined') {
        // Salva l'URL corrente come returnUrl
        const currentPath = window.location.pathname + window.location.search;
        // Configura il parametro returnUrl per la pagina di login
        sessionStorage.setItem('returnUrl', currentPath);
      }
    };

    return (
      <Link 
        href="/login" 
        className="text-sm font-medium text-blue-300 hover:text-white transition-colors py-2 px-3 rounded-md border border-blue-500/30 hover:bg-blue-500/10"
        onClick={handleLoginClick}
      >
        Area Riservata
      </Link>
    );
  }

  // Solo se l'utente è autenticato, mostra il menu utente
  return (
    <>
      <div className="relative inline-block">
        <button
          ref={buttonRef}
          onClick={() => {
            setIsOpen(!isOpen);
            if (!isOpen) {
              updateDropdownPosition();
            }
          }}
          className="flex items-center gap-1 px-3 py-1.5 bg-black/20 backdrop-blur-sm border border-blue-500/30 hover:bg-black/30 rounded-lg text-sm font-medium transition-colors"
        >
          {getDisplayName(player, user)}
          <ChevronDown size={16} className={`transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
        </button>
      </div>

      {isOpen && mounted && createPortal(
        <div
          ref={dropdownRef}
          className="absolute w-48 bg-gradient-to-br from-blue-950/90 via-sky-900/90 to-blue-900/90 backdrop-blur-sm border border-blue-500/30 rounded-lg shadow-lg z-50"
          style={{
            top: dropdownPosition.top,
            right: dropdownPosition.right,
          }}
        >
          <div className="py-1">
            {isAdmin ? (
              <>
                <Link
                  href="/admin/dashboard"
                  className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm text-blue-100 hover:text-white hover:bg-blue-900/50 transition-colors"
                  onClick={() => setIsOpen(false)}
                >
                  <Settings size={16} className="text-blue-300" />
                  Pannello Admin
                </Link>
                <Link
                  href="/profile"
                  className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm text-blue-100 hover:text-white hover:bg-blue-900/50 transition-colors"
                  onClick={() => setIsOpen(false)}
                >
                  <User size={16} className="text-blue-300" />
                  Profilo Giocatore
                </Link>
              </>
            ) : (
              <Link
                href="/profile"
                className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm text-blue-100 hover:text-white hover:bg-blue-900/50 transition-colors"
                onClick={() => setIsOpen(false)}
              >
                <User size={16} className="text-blue-300" />
                Profilo utente
              </Link>
            )}
            <button
              onClick={handleLogout}
              className="flex items-center gap-2 w-full text-left px-4 py-2 text-sm text-red-400 hover:text-red-300 hover:bg-red-900/30 transition-colors"
            >
              <LogOut size={16} className="text-red-400" />
              Disconnetti
            </button>
          </div>
        </div>,
        document.body
      )}
    </>
  );
}
