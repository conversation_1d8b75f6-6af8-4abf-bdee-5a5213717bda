'use client';

import { useState, useRef, useEffect } from 'react';
import { useAuthContext } from '@/lib/providers/AuthProvider';
import { LogOut, ChevronDown, Settings, User } from 'lucide-react';
import Link from 'next/link';
import { useIsAdmin } from '@/hooks/useIsAdmin';
import { getDisplayName } from '@/lib/utils';

export function UserMenu() {
  const { user, player, signOut, loading } = useAuthContext();
  const { isAdmin, loading: adminLoading } = useIsAdmin();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [mounted, setMounted] = useState(false);

  // Imposta mounted a true dopo il montaggio del componente
  useEffect(() => {
    setMounted(true);
  }, []);

  // Chiudi il dropdown quando si clicca fuori
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Salva l'URL corrente per il reindirizzamento post-logout
  const handleLogout = async () => {
    if (typeof window !== 'undefined') {
      // Salva l'URL corrente prima del logout
      const currentPath = window.location.pathname + window.location.search;
      localStorage.setItem('logoutReturnUrl', currentPath);
    }
    await signOut();
    setIsOpen(false);
  };

  // Non renderizzare nulla durante il rendering lato server o prima del montaggio
  // Questo previene errori di idratazione
  if (!mounted) {
    return (
      <div className="w-[120px] h-[36px] bg-blue-500/10 border border-blue-500/30 rounded-md"></div>
    );
  }

  // Mostra un placeholder durante il caricamento
  if (loading || adminLoading) {
    return (
      <div className="w-[120px] h-[36px] bg-blue-500/10 border border-blue-500/30 rounded-md"></div>
    );
  }

  // Se l'utente non è autenticato, mostra il pulsante "Area Riservata"
  if (!user) {
    // Salva l'URL corrente per il reindirizzamento post-login
    const handleLoginClick = () => {
      if (typeof window !== 'undefined') {
        // Salva l'URL corrente come returnUrl
        const currentPath = window.location.pathname + window.location.search;
        // Configura il parametro returnUrl per la pagina di login
        sessionStorage.setItem('returnUrl', currentPath);
      }
    };

    return (
      <Link 
        href="/login" 
        className="text-sm font-medium text-blue-300 hover:text-white transition-colors py-2 px-3 rounded-md border border-blue-500/30 hover:bg-blue-500/10"
        onClick={handleLoginClick}
      >
        Area Riservata
      </Link>
    );
  }

  // Solo se l'utente è autenticato, mostra il menu utente
  return (
    <div className="relative inline-block" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-1 px-3 py-1.5 bg-black/20 backdrop-blur-md border border-blue-500/30 hover:bg-black/30 rounded-lg text-sm font-medium transition-colors"
      >
        {getDisplayName(player, user)}
        <ChevronDown size={16} className={`transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute left-0 mt-2 w-48 bg-black/20 backdrop-blur-md border border-blue-500/30 rounded-lg shadow-lg z-[60] overflow-hidden animate-in fade-in-0 zoom-in-95 duration-200">
          <div className="py-2">
            {isAdmin ? (
              <>
                <Link
                  href="/admin/dashboard"
                  className="flex items-center gap-3 w-full px-4 py-3 text-left text-sm text-blue-100 hover:text-white hover:bg-blue-900/40 transition-all duration-200 first:rounded-t-xl"
                  onClick={() => setIsOpen(false)}
                >
                  <Settings size={16} className="text-blue-300" />
                  Pannello Admin
                </Link>
                <Link
                  href="/profile"
                  className="flex items-center gap-3 w-full px-4 py-3 text-left text-sm text-blue-100 hover:text-white hover:bg-blue-900/40 transition-all duration-200"
                  onClick={() => setIsOpen(false)}
                >
                  <User size={16} className="text-blue-300" />
                  Profilo Giocatore
                </Link>
              </>
            ) : (
              <Link
                href="/profile"
                className="flex items-center gap-3 w-full px-4 py-3 text-left text-sm text-blue-100 hover:text-white hover:bg-blue-900/40 transition-all duration-200 first:rounded-t-xl"
                onClick={() => setIsOpen(false)}
              >
                <User size={16} className="text-blue-300" />
                Profilo utente
              </Link>
            )}
            <div className="border-t border-blue-500/20 my-1"></div>
            <button
              onClick={handleLogout}
              className="flex items-center gap-3 w-full text-left px-4 py-3 text-sm text-red-200 hover:text-red-100 hover:bg-red-900/30 transition-all duration-200 last:rounded-b-xl"
            >
              <LogOut size={16} className="text-red-300" />
              Disconnetti
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
