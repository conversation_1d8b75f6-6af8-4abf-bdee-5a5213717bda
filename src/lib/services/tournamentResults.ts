import { supabase } from '@/lib/supabase/client';
import { Database } from '@/lib/supabase/types';
import { logger } from '@/lib/utils/logger';

type TournamentResult = Database['public']['Tables']['tournament_results']['Row'];

// Use a minimal public-safe player shape sourced from players_public
type PublicPlayer = { id: string; name: string };

// Type for registration with archetype join
type RegistrationWithArchetype = {
  player_id: string;
  archetype_id: string | null;
  archetypes: {
    id: string;
    name: string;
  } | null;
};

export interface TournamentResultWithPlayer extends TournamentResult {
  player: PublicPlayer | null;
  deck?: string; // This will need to be added to the database schema in the future
  archetype?: {
    id: string;
    name: string;
  } | null;
}

/**
 * Fetches tournament results for a specific tournament
 * @param tournamentId - The ID of the tournament
 * @returns Array of tournament results with player information
 */
export async function getTournamentResults(tournamentId: string): Promise<TournamentResultWithPlayer[]> {
  try {
    // 1) Fetch results without joining players (blocked by R<PERSON> for public)
    const { data, error } = await supabase
      .from('tournament_results')
      .select('*')
      .eq('tournament_id', tournamentId)
      .order('position', { ascending: true });

    if (error) {
      logger.error('Error fetching tournament results', error, {
        component: 'tournamentResults',
        tournamentId
      });
      throw error;
    }

    const rows = (data || []) as TournamentResult[];
    const playerIds = Array.from(new Set(rows.map(r => r.player_id)));

    // 2) Load public player names in one shot from players_public
    const playersById = new Map<string, PublicPlayer>();
    if (playerIds.length > 0) {
      const { data: ppl, error: pErr } = await supabase
        .from('players')
        .select('id, name')
        .in('id', playerIds as string[]);
      if (pErr) {
        logger.error('Error fetching players for results', pErr, { component: 'tournamentResults', tournamentId });
      } else {
        for (const p of (ppl as unknown as PublicPlayer[]) || []) playersById.set(p.id, p);
      }
    }

    // 3) Load archetype information from tournament registrations
    // Now that RLS policies allow anonymous users to see completed tournament registrations,
    // we can use the same query for all users
    const archetypesById = new Map<string, { id: string; name: string }>();
    if (playerIds.length > 0) {
      const { data: registrations, error: regErr } = await supabase
        .from('tournament_registrations')
        .select(`
          player_id,
          archetype_id,
          archetypes!inner (
            id,
            name
          )
        `)
        .eq('tournament_id', tournamentId)
        .in('player_id', playerIds as string[]);

      if (regErr) {
        logger.error('Error fetching archetypes for results', regErr, { component: 'tournamentResults', tournamentId });
      } else {
        for (const reg of (registrations || [])) {
          // Type assertion for the joined data
          const registration = reg as RegistrationWithArchetype;
          if (registration.player_id && registration.archetypes) {
            archetypesById.set(registration.player_id, {
              id: registration.archetypes.id,
              name: registration.archetypes.name
            });
          }
        }
      }
    }

    // 4) Map results + attach public-safe player object and archetype
    const results: TournamentResultWithPlayer[] = rows.map(result => ({
      ...result,
      player: playersById.get(result.player_id) || null,
      deck: undefined,
      archetype: archetypesById.get(result.player_id) || null,
    }));

    return results;
  } catch (error) {
    logger.error('Failed to fetch tournament results', error, {
      component: 'tournamentResults',
      tournamentId
    });
    throw error;
  }
}

/**
 * Creates or updates tournament results
 * @param results - Array of tournament results to save
 * @returns Success status
 */
export async function saveTournamentResults(
  tournamentId: string,
  results: Array<{
    player_id: string;
    position: number;
    points: number;
  }>
): Promise<boolean> {
  try {
    // First, delete existing results for this tournament
    const { error: deleteError } = await supabase
      .from('tournament_results')
      .delete()
      .eq('tournament_id', tournamentId);

    if (deleteError) {
      logger.error('Error deleting existing tournament results', deleteError, { 
        component: 'tournamentResults',
        tournamentId 
      });
      throw deleteError;
    }

    // Then, insert new results
    const resultsToInsert = results.map(result => ({
      ...result,
      tournament_id: tournamentId
    }));

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { error: insertError } = await (supabase as any)
      .from('tournament_results')
      .insert(resultsToInsert);

    if (insertError) {
      logger.error('Error inserting tournament results', insertError, { 
        component: 'tournamentResults',
        tournamentId 
      });
      throw insertError;
    }

    return true;
  } catch (error) {
    logger.error('Failed to save tournament results', error, { 
      component: 'tournamentResults',
      tournamentId 
    });
    throw error;
  }
}

/**
 * Checks if a tournament has results
 * @param tournamentId - The ID of the tournament
 * @returns Boolean indicating if results exist
 */
export async function hasResults(tournamentId: string): Promise<boolean> {
  try {
    const { count, error } = await supabase
      .from('tournament_results')
      .select('id', { count: 'exact', head: true })
      .eq('tournament_id', tournamentId);

    if (error) {
      logger.error('Error checking tournament results', error, { 
        component: 'tournamentResults',
        tournamentId 
      });
      return false;
    }

    return (count || 0) > 0;
  } catch (error) {
    logger.error('Failed to check tournament results', error, { 
      component: 'tournamentResults',
      tournamentId 
    });
    return false;
  }
}
