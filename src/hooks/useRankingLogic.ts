import { useState, useMemo, useCallback } from "react";
import { Player } from "@/types/ranking";

type SortConfig = {
  key: "name" | "totalPoints";
  direction: "asc" | "desc";
};

export function useRankingLogic(initialPlayers: Player[]) {
  const [searchQuery, setSearchQuery] = useState("");
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    key: "totalPoints",
    direction: "desc"
  });

  const filteredPlayers = useMemo(() => {
    // First, sort the initial players to establish the ranking order
    const sortedPlayers = [...initialPlayers].sort((a, b) => {
      if (sortConfig.key === "name") {
        return sortConfig.direction === "asc"
          ? a.name.localeCompare(b.name)
          : b.name.localeCompare(a.name);
      } else {
        const aPoints = a.totalPoints || 0;
        const bPoints = b.totalPoints || 0;
        return sortConfig.direction === "asc"
          ? aPoints - bPoints
          : bPoints - aPoints;
      }
    });

    // Add the original ranking position to each player
    const playersWithRanking = sortedPlayers.map((player, index) => ({
      ...player,
      originalRankingPosition: index + 1
    }));

    // Then filter based on search query
    return playersWithRanking.filter((player) => {
      const matchesSearch = player.name
        .toLowerCase()
        .includes(searchQuery.toLowerCase());
      return matchesSearch;
    });
  }, [initialPlayers, searchQuery, sortConfig]);

  const handleSort = useCallback((key: "name" | "totalPoints") => {
    setSortConfig((current) => ({
      key,
      direction:
        current.key === key && current.direction === "asc" ? "desc" : "asc",
    }));
  }, []);

  return {
    searchQuery,
    setSearchQuery,
    sortConfig,
    handleSort,
    filteredPlayers
  };
} 